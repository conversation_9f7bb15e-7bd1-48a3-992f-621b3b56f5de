/* 扫码登录按钮特殊样式 */
.qr-login-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.qr-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
}

.qr-login-btn:active {
  transform: translateY(0);
}

.qr-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.qr-login-btn:hover::before {
  left: 100%;
}

/* 二维码容器样式 */
.qr-code-wrapper {
  border: 3px solid #28a745;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
  transition: all 0.3s ease;
}

.qr-code-wrapper:hover {
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

/* 步骤指引样式 */
.step-item {
  text-align: center;
}

.step-number {
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* 手动输入按钮样式 */
.manual-input-btn {
  transition: all 0.3s ease;
}

.manual-input-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.2);
}

/* 等待提示样式 */
.bg-light-warning {
  background-color: #fff3cd !important;
}

.qr-loading-tip {
  animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}
