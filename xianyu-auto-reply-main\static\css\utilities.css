@media (max-width: 768px) {
  .container {
    margin-top: 1rem;
    padding: 0 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }

  .cookie-value {
    font-size: 0.75rem;
    max-height: 80px;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }

  /* 移动端商品表格优化 */
  #itemsTableBody .btn-group {
    flex-direction: row;
  }

  #itemsTableBody .btn-group .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
  }

  .status-toggle {
    width: 40px;
    height: 20px;
  }

  .status-slider:before {
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
  }

  input:checked + .status-slider:before {
    transform: translateX(20px);
  }

  .qr-login-btn, .manual-input-btn {
    margin-bottom: 10px;
    font-size: 0.9rem;
  }

  .step-item {
    margin-bottom: 15px;
  }

  .step-number {
    width: 25px !important;
    height: 25px !important;
    font-size: 12px;
  }

  .channel-type-card {
    margin-bottom: 10px;
    min-height: 160px; /* 移动端稍微减小高度 */
  }

  .channel-type-card .card-body {
    padding: 0.75rem; /* 移动端减少内边距 */
  }

  .channel-type-card .channel-icon i {
    font-size: 2rem !important; /* 移动端图标更小 */
  }

  .channel-type-card .card-title {
    font-size: 1rem; /* 移动端标题更小 */
  }

  .channel-type-card .card-text {
    font-size: 0.8rem; /* 移动端描述文字更小 */
  }
}

/* 商品管理表格优化 */
#itemsTableBody .btn-group {
  white-space: nowrap;
}

#itemsTableBody .btn-group .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

#itemsTableBody .btn-group .btn:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#itemsTableBody .btn-group .btn:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
}

#itemsTableBody .btn-group .btn i {
  font-size: 0.875rem;
}

/* 表格操作列样式 */
.table td:last-child {
  text-align: center;
  vertical-align: middle;
  padding: 0.5rem 0.25rem;
}

/* 表格文本截断优化 */
.table td {
  vertical-align: middle;
}

.table td[title] {
  cursor: help;
}

/* 商品表格特定优化 */
#itemsTableBody td:nth-child(4),
#itemsTableBody td:nth-child(5) {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .channel-type-card {
    min-height: 200px; /* 大屏幕稍微增加高度 */
  }
}
