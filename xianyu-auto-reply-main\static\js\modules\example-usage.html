<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲鱼自动回复系统 - 模块化版本</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 自定义CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 页面内容 -->
    <div id="app">
        <!-- 这里是原来的HTML内容 -->
        <div id="loading" class="d-none">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container position-fixed top-0 end-0 p-3"></div>
        
        <!-- 主要内容区域 -->
        <main>
            <!-- 这里放置原来的所有HTML内容 -->
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 
    模块化JavaScript文件 - 必须按以下顺序加载
    注意：这些路径需要根据实际部署情况调整
    -->
    
    <!-- 1. 全局变量 -->
    <script src="/static/js/modules/globals.js"></script>
    
    <!-- 2. 通用工具函数 -->
    <script src="/static/js/modules/utils.js"></script>
    
    <!-- 3. 页面导航功能 -->
    <script src="/static/js/modules/navigation.js"></script>
    
    <!-- 4. 仪表盘管理 -->
    <script src="/static/js/modules/dashboard.js"></script>
    
    <!-- 5. 关键词缓存管理 -->
    <script src="/static/js/modules/keyword-cache.js"></script>
    
    <!-- 6. 账号列表管理 -->
    <script src="/static/js/modules/account-list.js"></script>
    
    <!-- 7. 关键词管理 -->
    <script src="/static/js/modules/keywords.js"></script>
    
    <!-- 8. Cookie管理 -->
    <script src="/static/js/modules/cookies.js"></script>
    
    <!-- 9. 主要功能 -->
    <script src="/static/js/modules/main-features.js"></script>
    
    <!-- 10. 所有其他功能 -->
    <script src="/static/js/modules/all-other-features.js"></script>
    
    <!-- 11. 应用初始化（必须最后加载） -->
    <script src="/static/js/modules/init.js"></script>
    
    <!-- 可选：模块化主入口文件（用于调试和信息显示） -->
    <script src="/static/js/modules/app-modular.js"></script>

    <script>
        // 页面加载完成后的调试信息
        window.addEventListener('load', function() {
            console.log('页面加载完成');
            console.log('模块信息:', window.moduleInfo);
            
            // 检查关键函数是否可用
            const testFunctions = [
                'showSection',
                'loadDashboard',
                'showToast',
                'toggleLoading'
            ];
            
            testFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    console.log(`✓ ${funcName} 函数已加载`);
                } else {
                    console.error(`✗ ${funcName} 函数未找到`);
                }
            });
        });
    </script>
</body>
</html>
