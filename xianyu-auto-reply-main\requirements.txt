# ================================
# 闲鱼自动回复系统 - Python依赖包
# ================================

# ==================== 核心Web框架 ====================
fastapi>=0.111.0
uvicorn[standard]>=0.29.0
pydantic>=2.7.0

# ==================== 日志记录 ====================
loguru>=0.7.0

# ==================== 网络通信 ====================
websockets>=10.0,<13.0
aiohttp>=3.9.0
requests>=2.31.0
httpx>=0.25.0

# ==================== 配置文件处理 ====================
PyYAML>=6.0.0
python-dotenv>=1.0.1

# ==================== JavaScript执行引擎 ====================
PyExecJS>=1.5.1

# ==================== 协议缓冲区解析 ====================
blackboxprotobuf>=1.0.1

# ==================== 系统监控 ====================
psutil>=5.9.0

# ==================== 文件上传支持 ====================
python-multipart>=0.0.6

# ==================== AI回复引擎 ====================
openai>=1.65.5

# ==================== 图像处理 ====================
# 验证码生成、二维码生成
Pillow>=10.0.0
qrcode[pil]>=7.4.2

# ==================== 浏览器自动化 ====================
# 商品搜索、订单详情获取
playwright>=1.40.0

# ==================== 加密和安全 ====================
PyJWT>=2.8.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0

# ==================== 时间处理 ====================
python-dateutil>=2.8.2

# ==================== 正则表达式增强 ====================
regex>=2023.10.3

# ==================== Excel文件处理 ====================
# 数据导入导出功能
pandas>=2.0.0
openpyxl>=3.1.0

# ==================== 邮件发送 ====================
# 用户注册验证
email-validator>=2.0.0

# ==================== 其他工具库 ====================
typing-extensions>=4.7.0

# ==================== 说明 ====================
# 以下模块是Python内置模块，无需安装：
# - sqlite3 (数据库)
# - smtplib (邮件发送)
# - email (邮件处理)
# - json (JSON处理)
# - base64 (编码解码)
# - hashlib (哈希算法)
# - hmac (消息认证码)
# - time (时间处理)
# - datetime (日期时间)
# - os (操作系统接口)
# - sys (系统相关)
# - re (正则表达式)
# - urllib (URL处理)
# - asyncio (异步编程)
# - threading (多线程)
# - multiprocessing (多进程)
# - pathlib (路径处理)
# - uuid (UUID生成)
# - random (随机数)
# - secrets (安全随机数)
# - traceback (异常追踪)
# - logging (日志记录)
# - collections (集合类型)
# - itertools (迭代工具)
# - functools (函数工具)
# - operator (操作符函数)
# - copy (对象复制)
# - pickle (对象序列化)
# - gzip (压缩)
# - zlib (数据压缩)
# - zipfile (ZIP文件)
# - tarfile (TAR文件)
# - shutil (文件操作)
# - tempfile (临时文件)
# - io (输入输出)
# - csv (CSV文件)
# - xml (XML处理)
# - html (HTML处理)
# - http (HTTP客户端/服务器)
# - socket (网络编程)
# - ssl (SSL/TLS)
# - ftplib (FTP客户端)
# - poplib (POP3客户端)
# - imaplib (IMAP客户端)
# - telnetlib (Telnet客户端)
# - subprocess (子进程)
# - signal (信号处理)
# - atexit (退出处理)
# - weakref (弱引用)
# - gc (垃圾回收)
# - inspect (对象检查)
# - ast (抽象语法树)
# - dis (字节码反汇编)
# - keyword (关键字)
# - token (令牌)
# - tokenize (词法分析)
# - parser (语法分析)
# - symbol (符号)
# - code (代码对象)
# - codeop (代码编译)
# - py_compile (Python编译)
# - compileall (批量编译)
# - importlib (导入机制)
# - pkgutil (包工具)
# - modulefinder (模块查找)
# - runpy (运行Python模块)
# - argparse (命令行参数)
# - getopt (命令行选项)
# - optparse (选项解析)
# - configparser (配置文件)
# - fileinput (文件输入)
# - linecache (行缓存)
# - glob (文件名模式匹配)
# - fnmatch (文件名匹配)
# - difflib (差异比较)
# - textwrap (文本包装)
# - string (字符串)
# - struct (二进制数据)
# - codecs (编解码器)
# - unicodedata (Unicode数据)
# - stringprep (字符串预处理)
# - readline (行编辑)
# - rlcompleter (自动补全)
# - pprint (美化打印)
# - reprlib (repr替代)
# - enum (枚举)
# - numbers (数字抽象基类)
# - math (数学函数)
# - cmath (复数数学)
# - decimal (十进制浮点)
# - fractions (分数)
# - statistics (统计函数)
# - array (数组)
# - bisect (二分查找)
# - heapq (堆队列)
# - queue (队列)
# - types (动态类型)
# - contextlib (上下文管理)
# - abc (抽象基类)
# - atexit (退出处理)
# - traceback (异常追踪)
# - __future__ (未来特性)
# - warnings (警告)
# - dataclasses (数据类)
# - typing (类型提示)