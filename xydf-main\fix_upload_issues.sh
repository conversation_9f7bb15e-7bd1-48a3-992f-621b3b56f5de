#!/bin/bash

# xydf-main系统上传问题修复脚本

echo "🔧 开始修复xydf-main系统上传问题..."

# 1. 检查并创建上传目录
echo "📁 检查上传目录..."
UPLOAD_DIR="public/uploads"
if [ ! -d "$UPLOAD_DIR" ]; then
    mkdir -p "$UPLOAD_DIR"
    echo "✅ 创建上传目录: $UPLOAD_DIR"
fi

# 2. 设置目录权限
echo "🔐 设置目录权限..."
chmod -R 755 public/uploads/
chmod -R 755 public/assets/
chmod -R 755 runtime/

# 3. 检查Web服务器用户权限
echo "👤 检查Web服务器用户权限..."
chown -R www-data:www-data public/uploads/ 2>/dev/null || chown -R apache:apache public/uploads/ 2>/dev/null || chown -R nginx:nginx public/uploads/ 2>/dev/null

# 4. 创建当前日期的上传子目录
CURRENT_DATE=$(date +%Y%m%d)
DAILY_UPLOAD_DIR="public/uploads/$CURRENT_DATE"
if [ ! -d "$DAILY_UPLOAD_DIR" ]; then
    mkdir -p "$DAILY_UPLOAD_DIR"
    chmod 755 "$DAILY_UPLOAD_DIR"
    echo "✅ 创建日期目录: $DAILY_UPLOAD_DIR"
fi

# 5. 检查PHP配置
echo "🐘 检查PHP上传配置..."
php -r "
echo 'upload_max_filesize: ' . ini_get('upload_max_filesize') . PHP_EOL;
echo 'post_max_size: ' . ini_get('post_max_size') . PHP_EOL;
echo 'max_file_uploads: ' . ini_get('max_file_uploads') . PHP_EOL;
echo 'memory_limit: ' . ini_get('memory_limit') . PHP_EOL;
"

echo "✅ 修复脚本执行完成！"
echo "📝 请检查以下项目："
echo "   1. Web服务器是否有写入权限"
echo "   2. PHP上传配置是否合适"
echo "   3. 防火墙是否阻止了静态资源访问"
