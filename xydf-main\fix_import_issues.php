<?php
/**
 * xydf-main系统导入问题修复脚本
 */

echo "🔧 开始修复xydf-main系统导入问题...\n";

// 1. 检查上传目录权限
$uploadDir = __DIR__ . '/public/uploads';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
    echo "✅ 创建上传目录: $uploadDir\n";
}

// 设置目录权限
chmod($uploadDir, 0755);
echo "✅ 设置上传目录权限: 755\n";

// 2. 检查当前日期目录
$currentDate = date('Ymd');
$dailyUploadDir = $uploadDir . '/' . $currentDate;
if (!is_dir($dailyUploadDir)) {
    mkdir($dailyUploadDir, 0755, true);
    echo "✅ 创建日期目录: $dailyUploadDir\n";
}

// 3. 检查静态资源目录权限
$assetsDir = __DIR__ . '/public/assets';
if (is_dir($assetsDir)) {
    chmod($assetsDir, 0755);
    echo "✅ 设置静态资源目录权限: 755\n";
}

// 4. 检查runtime目录权限
$runtimeDir = __DIR__ . '/runtime';
if (is_dir($runtimeDir)) {
    chmod($runtimeDir, 0755);
    // 递归设置runtime子目录权限
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($runtimeDir),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($iterator as $item) {
        if ($item->isDir()) {
            chmod($item->getPathname(), 0755);
        }
    }
    echo "✅ 设置runtime目录权限: 755\n";
}

// 5. 创建正确的导入模板
$templateContent = "tb_name,tb_order_sn,tb_qr_url\n";
$templateContent .= "jekeu1,202507091123,/uploads/20250804/f3cf95a34097663504106b9aff0c85818.png\n";
$templateContent .= "shop2,202507091124,/uploads/20250804/another_qr_code.png\n";

file_put_contents(__DIR__ . '/product_import_template.csv', $templateContent);
echo "✅ 创建导入模板文件: product_import_template.csv\n";

// 6. 检查PHP配置
echo "\n📋 PHP配置检查:\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";

echo "\n✅ 修复脚本执行完成！\n";
echo "\n📝 导入说明:\n";
echo "1. 使用模板文件: product_import_template.csv\n";
echo "2. 表格格式: tb_name(店铺名称), tb_order_sn(订单号), tb_qr_url(二维码路径)\n";
echo "3. 确保二维码文件已上传到对应路径\n";
echo "4. 店铺名称必须在系统中存在\n";
?>
